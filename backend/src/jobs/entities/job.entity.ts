import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  OneToMany,
} from "typeorm";
import { Transform } from "class-transformer";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

import { JobStatus } from "../../common/enums/job-status.enum";
import { Application } from "src/applications/entities/application.entity";
import { Favorite } from "src/favorites/entities/favorite.entity";
import { User } from "src/users/entities/user.entity";

@Entity("jobs")
@Index(["latitude", "longitude"])
export class Job {
  @ApiProperty({ description: "Unique identifier" })
  @PrimaryGeneratedColumn("uuid")
  id!: string;

  @ApiProperty({ description: "Company ID" })
  @Column()
  @Index()
  companyId!: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: "companyId" })
  company!: User;

  @ApiProperty({ description: "Job title" })
  @Column()
  title!: string;

  @ApiProperty({ description: "Job description" })
  @Column("text")
  description!: string;

  @ApiProperty({ description: "Job location (address)" })
  @Column()
  location!: string;

  @ApiPropertyOptional({ description: "City" })
  @Column({ nullable: true })
  @Index()
  city?: string;

  @ApiPropertyOptional({ description: "State/Province" })
  @Column({ nullable: true })
  @Index()
  state?: string;

  @ApiPropertyOptional({ description: "Country" })
  @Column({ nullable: true })
  @Index()
  country?: string;

  @ApiPropertyOptional({ description: "Postal code" })
  @Column({ nullable: true, name: "postalCode" })
  postalCode?: string;

  @ApiPropertyOptional({ description: "Latitude coordinate" })
  @Column({ type: "decimal", precision: 10, scale: 6, nullable: true })
  @Index()
  latitude?: number;

  @ApiPropertyOptional({ description: "Longitude coordinate" })
  @Column({ type: "decimal", precision: 10, scale: 6, nullable: true })
  @Index()
  longitude?: number;

  @ApiProperty({ description: "Job start date and time" })
  @Column({ type: "timestamp" })
  @Transform(({ value }) => value && new Date(value))
  @Index()
  startDateTime!: Date;

  @ApiProperty({ description: "Job end date and time" })
  @Column({ type: "timestamp" })
  @Transform(({ value }) => value && new Date(value))
  @Index()
  endDateTime!: Date;

  @ApiProperty({ description: "Hourly rate" })
  @Column({ type: "decimal", precision: 10, scale: 2 })
  @Index()
  payRate!: number;

  @ApiProperty({ description: "Estimated hours" })
  @Column({ name: "estimatedHours", nullable: true })
  estimatedHours?: number;

  @ApiProperty({ description: "Pay rate type (hourly, daily, fixed)" })
  @Column({ default: "hourly" })
  payRateType!: string;

  @ApiProperty({ description: "Minimum trust score required", default: 0 })
  @Column({ default: 0 })
  @Index()
  trustScoreRequired!: number;

  @ApiProperty({ description: "Is this an emergency job?", default: false })
  @Column({ default: false })
  @Index()
  isEmergencyJob!: boolean;

  @ApiProperty({
    description: "Does this job require a laptop?",
    default: false,
  })
  @Column({ default: false })
  @Index()
  requiresLaptop!: boolean;

  @ApiProperty({
    description: "Does this job require a smartphone?",
    default: false,
  })
  @Column({ default: false })
  @Index()
  requiresSmartphone!: boolean;

  @ApiPropertyOptional({ description: "Additional requirements" })
  @Column({ nullable: true, type: "jsonb" })
  additionalRequirements?: AdditionalRequirementsDTO;

  @ApiProperty({
    description: "Job status",
    enum: JobStatus,
    default: JobStatus.OPEN,
  })
  @Column({
    type: "enum",
    enum: JobStatus,
    default: JobStatus.OPEN,
  })
  @Index()
  status!: JobStatus;

  @ApiPropertyOptional({ description: "Reason for cancellation" })
  @Column({ nullable: true })
  cancelledReason?: string;

  @ApiPropertyOptional({ description: "Cancellation date" })
  @Column({ nullable: true })
  @Transform(({ value }) => value && new Date(value))
  cancelledAt?: Date;

  @ApiProperty({
    description: "Maximum number of positions to fill",
    default: 1,
  })
  @Column({ default: 1 })
  maxPositions!: number;

  @ApiProperty({ description: "Number of positions filled", default: 0 })
  @Column({ default: 0 })
  filledPositions!: number;

  @ApiProperty({ description: "Creation date" })
  @CreateDateColumn()
  @Transform(({ value }) => value && new Date(value))
  createdAt!: Date;

  @ApiProperty({ description: "Last update date" })
  @UpdateDateColumn()
  @Transform(({ value }) => value && new Date(value))
  updatedAt!: Date;

  //relations
  @OneToMany(() => Application, (application) => application.worker)
  applications!: Application[];

  @OneToMany(() => Favorite, (application) => application.job)
  favorites!: Favorite[];
}

export class AdditionalRequirementsDTO {
  @ApiPropertyOptional({ description: "Required skills (comma separated)" })
  @Column({ nullable: true })
  requiredSkills?: string;

  @ApiPropertyOptional({ description: "Required experience" })
  @Column({ nullable: true })
  requiredExperience?: string;

  @ApiPropertyOptional({ description: "Required education" })
  @Column({ nullable: true })
  requiredEducation?: string;

  @ApiPropertyOptional({ description: "Required languages (comma separated)" })
  @Column({ nullable: true })
  requiredLanguages?: string;
}
