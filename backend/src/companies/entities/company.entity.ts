import {
  En<PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
} from "typeorm";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { User } from "../../users/entities/user.entity";

@Entity("companies")
export class Company {
  @ApiPropertyOptional({ description: "Company registration number" })
  @Column({ nullable: true })
  registrationNumber?: string;

  @ApiPropertyOptional({ description: "Company tax ID" })
  @Column({ nullable: true })
  taxId?: string;

  @ApiPropertyOptional({ description: "Company website" })
  @Column({ nullable: true })
  website?: string;

  @ApiPropertyOptional({ description: "Company size" })
  @Column({ nullable: true })
  size?: string;

  @ApiPropertyOptional({ description: "Company industry" })
  @Column({ nullable: true })
  industry?: string;

  @ApiPropertyOptional({ description: "Year the company was founded" })
  @Column({ nullable: true })
  foundedYear?: number;

  @ApiPropertyOptional({ description: "Headquarters address" })
  @Column({ nullable: true, name: "headquartersAddress" })
  headquartersAddress?: string;

  @ApiPropertyOptional({ description: "Headquarters city" })
  @Column({ nullable: true, name: "headquartersCity" })
  headquartersCity?: string;

  @ApiPropertyOptional({ description: "Headquarters state/province" })
  @Column({ nullable: true, name: "headquartersState" })
  headquartersState?: string;

  @ApiPropertyOptional({ description: "Headquarters postal code" })
  @Column({ nullable: true, name: "headquartersPostalCode" })
  headquartersPostalCode?: string;

  @ApiPropertyOptional({ description: "Headquarters country" })
  @Column({ nullable: true, name: "headquartersCountry" })
  headquartersCountry?: string;

  @ApiProperty({ description: "User ID associated with this company" })
  @Column()
  userId!: string;

  // Relations
  @OneToOne(() => User)
  @JoinColumn({ name: "userId" })
  user!: User;

  @ApiProperty({ description: "Creation date" })
  @CreateDateColumn()
  createdAt!: Date;

  @ApiProperty({ description: "Last update date" })
  @UpdateDateColumn()
  updatedAt!: Date;
}
